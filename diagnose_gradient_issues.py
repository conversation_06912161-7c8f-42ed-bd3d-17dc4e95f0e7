#!/usr/bin/env python3
"""
诊断DC token梯度消失问题的脚本
分析梯度传播路径和数值稳定性
"""

import torch
import torch.nn.functional as F
import numpy as np
import logging
from pathlib import Path
import sys

# 添加lerobot路径
sys.path.insert(0, str(Path(__file__).parent))

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionModel
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC, ContrastiveLoss

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_test_model():
    """创建测试模型"""
    config = DiffusionConfig(
        horizon=16,
        n_obs_steps=2,
        diffusion_step_embed_dim=128,
        n_layer=4,
        n_head=4,
        n_cond_layers=2,
        use_transformer=True,
        input_shapes={
            "observation.state": [32],
            "observation.image": [3, 96, 96],
        },
        output_shapes={
            "action": [7],
        }
    )
    
    model = DiffusionModel_DC(
        config=config,
        n_dc_tokens=4,
        n_dc_layers=3,
        use_dc_t=True,
        cond_dim=32
    )
    
    model.initialize_dc_tokens()
    model.freeze_base_model()
    
    return model

def create_test_batch(batch_size=4, device='cpu'):
    """创建测试批次"""
    return {
        "action": torch.randn(batch_size, 16, 7, device=device),
        "observation.state": torch.randn(batch_size, 2, 32, device=device),
        "observation.image": torch.randn(batch_size, 2, 3, 96, 96, device=device),
    }

def analyze_gradient_magnitudes(model, batch):
    """分析梯度大小分布"""
    print("\n🔍 分析梯度大小分布")
    
    # 创建对比损失
    contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device='cpu')
    
    # 前向传播
    error_matrix = model.compute_contrastive_error(batch, use_dc=True)
    loss = contrastive_loss(error_matrix)
    
    print(f"📊 误差矩阵统计:")
    print(f"   形状: {error_matrix.shape}")
    print(f"   均值: {error_matrix.mean().item():.6f}")
    print(f"   标准差: {error_matrix.std().item():.6f}")
    print(f"   最小值: {error_matrix.min().item():.6f}")
    print(f"   最大值: {error_matrix.max().item():.6f}")
    
    print(f"📊 损失值: {loss.item():.6f}")
    
    # 反向传播
    model.zero_grad()
    loss.backward()
    
    # 分析梯度
    print(f"\n📈 梯度分析:")
    dc_grad_info = {}
    base_grad_info = {}
    
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = torch.norm(param.grad).item()
            grad_mean = param.grad.mean().item()
            grad_std = param.grad.std().item()
            grad_max = param.grad.max().item()
            grad_min = param.grad.min().item()
            
            grad_info = {
                'norm': grad_norm,
                'mean': grad_mean,
                'std': grad_std,
                'max': grad_max,
                'min': grad_min,
                'shape': param.grad.shape
            }
            
            is_dc_param = (
                'dc_token' in name or
                (hasattr(model.net, 'dc_tokens') and param is model.net.dc_tokens) or
                (hasattr(model.net, 'dc_t_tokens') and param is model.net.dc_t_tokens.weight)
            )
            
            if is_dc_param:
                dc_grad_info[name] = grad_info
                print(f"   DC {name}: norm={grad_norm:.2e}, mean={grad_mean:.2e}, std={grad_std:.2e}")
            else:
                base_grad_info[name] = grad_info
    
    print(f"\n📊 梯度统计:")
    print(f"   DC参数梯度数量: {len(dc_grad_info)}")
    print(f"   基础参数梯度数量: {len(base_grad_info)}")
    
    if dc_grad_info:
        dc_norms = [info['norm'] for info in dc_grad_info.values()]
        print(f"   DC梯度范数: 最小={min(dc_norms):.2e}, 最大={max(dc_norms):.2e}, 均值={np.mean(dc_norms):.2e}")
    
    return dc_grad_info, base_grad_info

def analyze_loss_sensitivity(model, batch):
    """分析损失对DC tokens的敏感性"""
    print("\n🎯 分析损失敏感性")
    
    contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device='cpu')
    
    # 获取原始损失
    with torch.no_grad():
        original_error = model.compute_contrastive_error(batch, use_dc=True)
        original_loss = contrastive_loss(original_error)
        print(f"原始损失: {original_loss.item():.6f}")
    
    # 扰动DC tokens并观察损失变化
    perturbation_scales = [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1]
    
    for scale in perturbation_scales:
        # 保存原始值
        original_dc_tokens = model.net.dc_tokens.data.clone()
        original_dc_t_tokens = model.net.dc_t_tokens.weight.data.clone()
        
        # 添加扰动
        model.net.dc_tokens.data += scale * torch.randn_like(model.net.dc_tokens.data)
        model.net.dc_t_tokens.weight.data += scale * torch.randn_like(model.net.dc_t_tokens.weight.data)
        
        # 计算新损失
        with torch.no_grad():
            perturbed_error = model.compute_contrastive_error(batch, use_dc=True)
            perturbed_loss = contrastive_loss(perturbed_error)
            loss_change = abs(perturbed_loss.item() - original_loss.item())
            
        print(f"扰动规模 {scale:.0e}: 损失变化 {loss_change:.2e}")
        
        # 恢复原始值
        model.net.dc_tokens.data = original_dc_tokens
        model.net.dc_t_tokens.weight.data = original_dc_t_tokens

def analyze_temperature_effect(model, batch):
    """分析温度参数对梯度的影响"""
    print("\n🌡️ 分析温度参数效应")
    
    temperatures = [0.01, 0.05, 0.07, 0.1, 0.2, 0.5, 1.0]
    
    for temp in temperatures:
        contrastive_loss = ContrastiveLoss(temp=temp, scale=4.0, device='cpu')
        
        # 前向传播
        error_matrix = model.compute_contrastive_error(batch, use_dc=True)
        loss = contrastive_loss(error_matrix)
        
        # 反向传播
        model.zero_grad()
        loss.backward()
        
        # 计算DC梯度范数
        dc_grad_norm = 0.0
        for name, param in model.named_parameters():
            if param.grad is not None and 'dc_token' in name:
                dc_grad_norm += torch.norm(param.grad).item() ** 2
        dc_grad_norm = dc_grad_norm ** 0.5
        
        print(f"温度 {temp:.3f}: 损失={loss.item():.4f}, DC梯度范数={dc_grad_norm:.2e}")

def test_direct_dc_loss(model, batch):
    """测试直接的DC token损失"""
    print("\n🎯 测试直接DC token损失")
    
    # 创建简单的DC token损失
    def simple_dc_loss(model):
        loss = 0.0
        if hasattr(model.net, 'dc_tokens'):
            # 简单的L2损失，鼓励DC tokens不为零
            loss += torch.sum(model.net.dc_tokens ** 2)
        if hasattr(model.net, 'dc_t_tokens'):
            loss += torch.sum(model.net.dc_t_tokens.weight ** 2)
        return loss
    
    # 测试直接损失的梯度
    loss = simple_dc_loss(model)
    print(f"直接DC损失: {loss.item():.6f}")
    
    model.zero_grad()
    loss.backward()
    
    # 检查梯度
    for name, param in model.named_parameters():
        if param.grad is not None and 'dc_token' in name:
            grad_norm = torch.norm(param.grad).item()
            print(f"直接损失 - {name}: 梯度范数={grad_norm:.2e}")

def main():
    """主函数"""
    print("🚀 DC token梯度问题诊断")
    
    # 创建模型和数据
    model = create_test_model()
    batch = create_test_batch(batch_size=4)
    
    print(f"✅ 模型创建成功")
    print(f"   DC tokens形状: {model.net.dc_tokens.shape}")
    print(f"   DC_t tokens形状: {model.net.dc_t_tokens.weight.shape}")
    
    # 运行诊断
    try:
        analyze_gradient_magnitudes(model, batch)
        analyze_loss_sensitivity(model, batch)
        analyze_temperature_effect(model, batch)
        test_direct_dc_loss(model, batch)
        
        print("\n" + "="*60)
        print("📊 诊断总结:")
        print("1. 检查梯度大小分布 - 完成")
        print("2. 检查损失敏感性 - 完成")
        print("3. 检查温度参数效应 - 完成")
        print("4. 检查直接DC损失 - 完成")
        
        print("\n💡 建议的解决方案:")
        print("1. 大幅增加学习率 (1000倍以上)")
        print("2. 增加温度参数 (0.5或更高)")
        print("3. 增加scale参数 (10倍以上)")
        print("4. 添加直接的DC正则化损失")
        print("5. 使用SGD而不是Adam优化器")
        print("6. 完全移除梯度裁剪")
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
