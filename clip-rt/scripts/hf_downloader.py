#!/usr/bin/env python3
"""
Hugging Face模型仓库高速下载脚本
支持整个仓库下载、国内镜像加速、断点续传、多线程下载
"""

import os
import sys
import argparse
import requests
from urllib.parse import urlparse
from pathlib import Path
import time
from tqdm import tqdm
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 国内镜像站点配置
MIRROR_SITES = {
    'hf-mirror': 'https://hf-mirror.com',
    'modelscope': 'https://www.modelscope.cn/models',
    'original': 'https://huggingface.co'
}

def get_repo_files_list(repo_id, mirror='hf-mirror', branch='main', token=None):
    """获取仓库文件列表"""
    base_url = MIRROR_SITES[mirror]
    
    # 设置认证头
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # 尝试不同的API端点
    api_endpoints = [
        f"{base_url}/api/models/{repo_id}/tree/{branch}?recursive=true",
        f"{base_url}/api/models/{repo_id}",
        f"https://huggingface.co/api/models/{repo_id}/tree/{branch}?recursive=true"
    ]
    
    for api_url in api_endpoints:
        try:
            print(f"尝试API: {api_url}")
            response = requests.get(api_url, headers=headers, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            files = []
            
            # 处理不同的响应格式
            if isinstance(data, list):
                # 直接是文件列表
                for item in data:
                    if item.get('type') == 'file':
                        files.append(item['path'])
            elif 'siblings' in data:
                # huggingface.co API格式
                for item in data['siblings']:
                    files.append(item['rfilename'])
            
            if files:
                return files
                
        except Exception as e:
            print(f"API调用失败: {e}")
            continue
    
    return []

def get_repo_files_list_fallback(repo_id, branch='main'):
    """备用方案：使用预定义的文件列表"""
    
    if 'stable-diffusion-3' in repo_id:
        # Stable Diffusion 3 的完整文件列表
        sd3_files = [
            '.gitattributes',
            'LICENSE', 
            'README.md',
            'mmdit.png',
            'model_index.json',
            'sd3demo.jpg',
            'scheduler/scheduler_config.json',
            'text_encoder/config.json',
            'text_encoder/model.safetensors',
            'text_encoder_2/config.json', 
            'text_encoder_2/model.safetensors',
            'text_encoder_3/config.json',
            'text_encoder_3/model.safetensors',
            'transformer/config.json',
            'transformer/diffusion_pytorch_model.safetensors',
            'vae/config.json',
            'vae/diffusion_pytorch_model.safetensors',
            'tokenizer/merges.txt',
            'tokenizer/special_tokens_map.json',
            'tokenizer/tokenizer.json',
            'tokenizer/tokenizer_config.json',
            'tokenizer/vocab.json',
            'tokenizer_2/merges.txt',
            'tokenizer_2/special_tokens_map.json', 
            'tokenizer_2/tokenizer.json',
            'tokenizer_2/tokenizer_config.json',
            'tokenizer_2/vocab.json',
            'tokenizer_3/special_tokens_map.json',
            'tokenizer_3/spiece.model',
            'tokenizer_3/tokenizer.json',
            'tokenizer_3/tokenizer_config.json'
        ]
        return sd3_files
    else:
        # 通用文件列表
        common_files = [
            'model_index.json',
            'scheduler/scheduler_config.json',
            'text_encoder/config.json',
            'text_encoder/pytorch_model.bin',
            'text_encoder_2/config.json', 
            'text_encoder_2/pytorch_model.bin',
            'transformer/config.json',
            'transformer/diffusion_pytorch_model.safetensors',
            'vae/config.json',
            'vae/diffusion_pytorch_model.safetensors',
            'README.md',
            '.gitattributes'
        ]
        return common_files

def parse_repo_url_or_id(input_str):
    """解析仓库URL或ID"""
    if input_str.startswith('http'):
        # URL格式: https://huggingface.co/stabilityai/stable-diffusion-3-medium-diffusers
        parts = input_str.split('/')
        if len(parts) >= 5:
            repo_id = f"{parts[-2]}/{parts[-1]}"
        else:
            raise ValueError("URL格式错误")
    else:
        # 直接是repo_id格式: stabilityai/stable-diffusion-3-medium-diffusers
        repo_id = input_str
    
    return repo_id

def get_download_url(repo_id, file_path, mirror='hf-mirror', branch='main'):
    """构建下载URL"""
    base_url = MIRROR_SITES[mirror]
    
    if mirror == 'modelscope':
        return f"{base_url}/{repo_id}/resolve/{branch}/{file_path}"
    else:
        return f"{base_url}/{repo_id}/resolve/{branch}/{file_path}"

def download_file_with_progress(url, local_path, resume=True, token=None):
    """带进度条的文件下载，支持断点续传"""
    
    # 设置认证头
    headers = {}
    if token:
        headers['Authorization'] = f'Bearer {token}'
    
    # 检查是否需要断点续传
    resume_header = {}
    initial_pos = 0
    
    if resume and os.path.exists(local_path):
        initial_pos = os.path.getsize(local_path)
        resume_header['Range'] = f'bytes={initial_pos}-'
    
    # 合并headers
    headers.update(resume_header)
    
    try:
        # 获取文件信息
        response = requests.head(url, headers=headers, timeout=30)
        total_size = int(response.headers.get('content-length', 0))
        
        if resume and initial_pos > 0:
            if initial_pos >= total_size:
                return True
        
        # 开始下载
        download_headers = headers.copy()
        response = requests.get(url, headers=download_headers, stream=True, timeout=30)
        response.raise_for_status()
        
        # 如果服务器不支持断点续传，重新下载
        if resume and response.status_code != 206 and initial_pos > 0:
            initial_pos = 0
            download_headers = {'Authorization': f'Bearer {token}'} if token else {}
            response = requests.get(url, headers=download_headers, stream=True, timeout=30)
        
        # 创建目录
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 写入文件
        mode = 'ab' if resume and initial_pos > 0 else 'wb'
        
        with open(local_path, mode) as f:
            with tqdm(
                total=total_size,
                initial=initial_pos,
                unit='B',
                unit_scale=True,
                desc=os.path.basename(local_path),
                leave=False
            ) as pbar:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
        
        return True
        
    except Exception as e:
        print(f"下载失败 {os.path.basename(local_path)}: {e}")
        return False

def download_single_file(repo_id, file_path, output_dir, mirrors, branch='main', token=None):
    """下载单个文件，尝试多个镜像"""
    local_path = os.path.join(output_dir, repo_id, file_path)
    
    # 如果文件已存在且大小正确，跳过
    if os.path.exists(local_path):
        print(f"⏭️  跳过已存在的文件: {file_path}")
        return True
    
    for mirror in mirrors:
        download_url = get_download_url(repo_id, file_path, mirror, branch)
        
        if download_file_with_progress(download_url, local_path, token=token):
            print(f"✅ 完成: {file_path}")
            return True
        
        time.sleep(1)
    
    print(f"❌ 所有镜像都失败: {file_path}")
    return False

def download_repo(repo_id, output_dir, mirrors, max_workers=4, branch='main', token=None):
    """下载整个仓库"""
    print(f"🔍 获取仓库文件列表: {repo_id}")
    
    # 尝试获取文件列表
    files = get_repo_files_list(repo_id, mirrors[0], branch, token)
    if not files:
        files = get_repo_files_list_fallback(repo_id, branch)
    
    if not files:
        print("❌ 无法获取文件列表")
        return False
    
    print(f"📦 找到 {len(files)} 个文件")
    
    # 并行下载
    success_count = 0
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_file = {
            executor.submit(download_single_file, repo_id, file_path, output_dir, mirrors, branch, token): file_path
            for file_path in files
        }
        
        with tqdm(total=len(files), desc="总进度") as pbar:
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    if future.result():
                        success_count += 1
                except Exception as e:
                    print(f"下载出错 {file_path}: {e}")
                pbar.update(1)
    
    print(f"\n🎉 下载完成！成功: {success_count}/{len(files)}")
    return success_count > 0

def main():
    parser = argparse.ArgumentParser(description='Hugging Face模型仓库下载工具')
    parser.add_argument('repo', help='仓库ID或URL (例如: stabilityai/stable-diffusion-3-medium-diffusers)')
    parser.add_argument('-o', '--output', default='./models', help='输出目录（默认: ./models）')
    parser.add_argument('-m', '--mirror', choices=['hf-mirror', 'modelscope', 'original'], 
                       default='hf-mirror', help='首选镜像站点')
    parser.add_argument('-j', '--jobs', type=int, default=4, help='并发下载数（默认: 4）')
    parser.add_argument('-b', '--branch', default='main', help='分支名（默认: main）')
    parser.add_argument('-t', '--token', help='HuggingFace访问令牌（用于私有模型）')
    parser.add_argument('--list-only', action='store_true', help='仅列出文件，不下载')
    
    args = parser.parse_args()
    
    # 检查环境变量中的token
    token = args.token or os.getenv('HF_TOKEN') or os.getenv('HUGGING_FACE_HUB_TOKEN')
    
    try:
        # 解析仓库ID
        repo_id = parse_repo_url_or_id(args.repo)
        print(f"📦 仓库: {repo_id}")
        print(f"💾 输出目录: {args.output}")
        
        if token:
            print("🔑 使用认证令牌")
        else:
            print("⚠️  未提供认证令牌，某些模型可能无法下载")
        
        # 选择镜像优先级
        if args.mirror == 'hf-mirror':
            mirrors = ['hf-mirror', 'original']
        elif args.mirror == 'modelscope':
            mirrors = ['modelscope', 'hf-mirror', 'original']
        else:
            mirrors = ['original']
        
        # 如果只是列出文件
        if args.list_only:
            files = get_repo_files_list(repo_id, mirrors[0], args.branch, token)
            if not files:
                files = get_repo_files_list_fallback(repo_id, args.branch)
            
            print(f"\n📄 文件列表 ({len(files)} 个文件):")
            for file_path in files:
                print(f"  {file_path}")
            return
        
        # 开始下载
        success = download_repo(repo_id, args.output, mirrors, args.jobs, args.branch, token)
        
        if success:
            final_path = os.path.join(args.output, repo_id)
            print(f"📁 模型位置: {final_path}")
        else:
            print("💥 下载失败！")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()