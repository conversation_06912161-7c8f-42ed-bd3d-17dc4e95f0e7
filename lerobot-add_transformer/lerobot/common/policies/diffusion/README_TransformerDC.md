# DiffusionModel_DC with SoftREPA-style Contrastive Learning

这个模块实现了基于SoftREPA思想的DiffusionModel_DC，用于机器人动作扩散模型的对比学习训练。该实现扩展了现有的`DiffusionModel`类，添加了DC token功能和对比学习机制。

## 核心特性

### 1. DC Token机制
- **可学习的软提示**：每层独立的DC tokens，用于改善动作-观测对齐
- **时间依赖性**：支持时间步相关的DC token调制
- **层级渐进**：前n层使用DC tokens，实现渐进式特征学习

### 2. 对比学习训练
- **单步去噪**：避免昂贵的多步采样，提高训练效率
- **批内对比**：每个动作与所有观测配对，学习相对对齐关系
- **参数高效**：只训练DC tokens，冻结预训练权重

### 3. 推理增强
- **标准采样**：支持DDPM多步去噪采样
- **引导采样**：支持classifier-free guidance
- **DC开关**：可选择是否使用DC tokens进行推理

## 文件结构

```
lerobot/common/policies/diffusion/
├── transformer_dc_sampler.py      # 核心采样器实现
├── transformer_dc_training.py     # 训练脚本和配置
└── README_TransformerDC.md        # 本文档

examples/
└── transformer_dc_example.py      # 使用示例
```

## 快速开始

### 1. 基本使用

```python
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import create_diffusion_model_dc
from lerobot.common.policies.diffusion.transformer_dc_training import create_trainer

# 创建配置
diffusion_config = DiffusionConfig(
    horizon=16,
    action_feature=torch.zeros(7),  # 7维动作
    diffusion_step_embed_dim=256,
    n_layer=6,
    n_head=8,
    n_obs_steps=1,
    n_cond_layers=4,
    use_transformer=True,  # 启用transformer
    robot_state_feature=torch.zeros(64),  # 机器人状态特征
)

# 方式1: 从预训练模型创建（推荐）
diffusion_model = create_diffusion_model_dc(
    config=diffusion_config,
    n_dc_tokens=4,
    n_dc_layers=6,
    use_dc_t=True,
    pretrained_model_path="path/to/pretrained_diffusion_model.pth"  # 关键！
)

# 方式2: 从头创建（不推荐，仅用于测试）
diffusion_model = create_diffusion_model_dc(
    config=diffusion_config,
    n_dc_tokens=4,
    n_dc_layers=6,
    use_dc_t=True
    # 不指定pretrained_model_path
)

# 创建训练器
trainer = create_trainer(
    diffusion_config=diffusion_config,
    cond_dim=64,  # 由robot_state_feature决定
    save_dir="./checkpoints"
)
```

### 2. 预训练模型的重要性

**SoftREPA的核心思想**：基于预训练的扩散模型进行微调，而不是从头训练。

```python
# ✅ 正确方式：从预训练模型开始
training_config = TransformerDCTrainingConfig()
training_config.pretrained_model_path = "path/to/pretrained_diffusion_model.pth"

# ❌ 错误方式：从头训练（效果差）
training_config.pretrained_model_path = None
```

**预训练模型来源**：
- 在相同任务上训练的DiffusionModel检查点
- 在相似任务上训练的模型（迁移学习）
- 官方发布的预训练权重

### 3. 训练模型

```python
# 准备数据
train_dataloader = ...  # 包含'action'和'observation'的DataLoader
val_dataloader = ...

# 开始训练（只训练DC tokens，其他权重冻结）
trainer.train(train_dataloader, val_dataloader)
```

### 3. 推理生成

```python
from lerobot.common.policies.diffusion.transformer_dc_sampler import TransformerDCInference

# 创建推理引擎
inference = TransformerDCInference(diffusion_model)

# 准备观测数据（与训练时格式相同）
batch = {
    'observation.state': torch.randn(4, 1, 64)  # (batch_size, n_obs_steps, state_dim)
}

# 生成动作序列
actions = inference.sample(
    batch=batch,
    use_dc=True
)

# 带引导的采样
guided_actions = inference.sample_with_guidance(
    batch=batch,
    guidance_scale=2.0,
    use_dc=True
)
```

## 配置参数

### TransformerDCSamplerConfig
- `n_dc_tokens`: 每层DC token数量 (默认: 4)
- `n_dc_layers`: 使用DC tokens的层数 (默认: 6)
- `use_dc_t`: 是否使用时间依赖的DC tokens (默认: True)
- `temp`: 对比学习温度参数 (默认: 0.07)
- `scale`: 对比学习缩放参数 (默认: 4.0)
- `dweight`: 扩散损失权重 (默认: 0.0)

### TransformerDCTrainingConfig
- `batch_size`: 批大小 (默认: 64)
- `learning_rate`: 学习率 (默认: 1e-4)
- `epochs`: 训练轮数 (默认: 100)
- `use_amp`: 是否使用混合精度 (默认: True)
- `gradient_clip_norm`: 梯度裁剪范数 (默认: 1.0)

## 运行示例

```bash
# 训练模式
python examples/transformer_dc_example.py \
    --mode train \
    --epochs 50 \
    --batch_size 32 \
    --n_dc_tokens 4 \
    --n_dc_layers 6 \
    --use_dc_t \
    --use_wandb

# 推理模式
python examples/transformer_dc_example.py \
    --mode inference \
    --checkpoint ./checkpoints/best_checkpoint.pt

# 训练+推理
python examples/transformer_dc_example.py \
    --mode both \
    --epochs 20 \
    --batch_size 16
```

## 核心算法

### 1. DC Token注入机制

```python
# 在Transformer的前n_dc_layers层中
for layer_idx in range(n_dc_layers):
    # 获取当前层的DC tokens
    dc_tokens = self.dc_tokens[layer_idx]  # (n_dc_tokens, embed_dim)
    
    # 添加时间依赖性（可选）
    if use_dc_t:
        time_component = self.dc_t_tokens(timestep // 10)
        dc_tokens = dc_tokens + time_component
    
    # 与条件信息拼接
    enhanced_condition = torch.cat([dc_tokens, original_condition], dim=1)
```

### 2. 对比学习损失

```python
# 构建对比矩阵
error_matrix = torch.zeros(batch_size, batch_size)
for i in range(batch_size):
    for j in range(batch_size):
        error_matrix[i, j] = mse_loss(
            predict_noise(action_i, condition_j),
            true_noise_i
        )

# 对比损失
positive_mask = torch.eye(batch_size)  # 对角线为正样本
logits = scale * torch.exp(-error_matrix / temperature)
loss = cross_entropy(logits, positive_mask)
```

### 3. 单步训练vs多步推理

**训练时（单步）**：
```python
# 只需要一次前向传播
noisy_action = add_noise(clean_action, noise, timestep)
pred_noise = model(noisy_action, timestep, condition)
loss = mse_loss(pred_noise, noise)
```

**推理时（多步）**：
```python
# DDPM采样循环
action = random_noise()
for t in reversed(range(1000)):
    pred_noise = model(action, t, condition)
    action = scheduler.step(pred_noise, t, action)
```

## 与原版SoftREPA的差异

| 特性 | SoftREPA (图像生成) | TransformerDC (动作生成) |
|------|-------------------|------------------------|
| **数据类型** | 2D图像潜在表示 | 1D动作序列 |
| **条件信息** | 文本嵌入 | 机器人观测 |
| **架构** | UNet/Transformer2D | Transformer1D |
| **DC注入** | 交叉注意力层 | 编码器条件层 |
| **应用场景** | 文本-图像对齐 | 动作-观测对齐 |

## 性能优势

1. **训练效率**：单步去噪 vs 多步采样，速度提升约50倍
2. **内存效率**：只训练DC tokens，参数量减少99%+
3. **对齐精度**：对比学习显著改善动作-观测匹配
4. **推理灵活**：支持有/无DC tokens的推理切换

## 注意事项

1. **数据格式**：确保数据包含'action'和'observation'字段
2. **设备同步**：所有张量需要在相同设备上
3. **梯度设置**：只有DC相关参数需要梯度
4. **时间步范围**：训练时间步应在[0, 999]范围内

## 扩展方向

1. **多模态条件**：支持图像+状态的联合条件
2. **层级DC tokens**：不同层使用不同类型的DC tokens
3. **自适应温度**：动态调整对比学习温度参数
4. **迁移学习**：从预训练模型初始化DC tokens

## 故障排除

### 常见问题

1. **CUDA内存不足**：减少batch_size或使用gradient_checkpointing
2. **训练不收敛**：调整learning_rate和温度参数
3. **推理质量差**：检查DC tokens是否正确加载
4. **速度慢**：确保使用混合精度训练

### 调试技巧

```python
# 检查可训练参数
trainable_params = sampler.get_trainable_parameters()
print(f"Trainable parameters: {len(trainable_params)}")

# 验证DC tokens梯度
for name, param in sampler.named_parameters():
    if 'dc' in name:
        print(f"{name}: requires_grad={param.requires_grad}")

# 监控对比损失
print(f"Diagonal error: {torch.diag(error_matrix).mean()}")
print(f"Off-diagonal error: {error_matrix[~torch.eye(B, dtype=bool)].mean()}")
```
