"""
Simple test script for TransformerForDiffusion_DC sampler.
This script verifies that the implementation works correctly.
"""

import torch
import sys
import os

# Add the lerobot path
sys.path.append('/home/<USER>/work/lerobot-add_transformer')

from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    create_diffusion_model_dc,
    TransformerDCInference,
    SoftREPATrainer,
    ContrastiveLoss
)


def test_diffusion_model_creation():
    """Test basic diffusion model creation"""
    print("Testing diffusion model creation...")

    # Create config
    config = DiffusionConfig(
        horizon=16,
        action_feature=torch.zeros(7),
        diffusion_step_embed_dim=128,
        n_layer=4,
        n_head=4,
        n_obs_steps=1,
        n_cond_layers=2,
        causal_attn=True,
        p_drop_emb=0.1,
        p_drop_attn=0.1,
        use_transformer=True,  # Enable transformer
        robot_state_feature=torch.zeros(64),  # Add required robot state feature
        image_features=None,  # No image features for testing
        env_state_feature=None  # No env state features
    )

    # Create diffusion model with DC tokens
    diffusion_model = create_diffusion_model_dc(
        config=config,
        n_dc_tokens=4,
        n_dc_layers=3,
        use_dc_t=True
    )

    print(f"✓ DiffusionModel_DC created successfully")
    print(f"  - DC tokens shape: {diffusion_model.net.dc_tokens.shape}")
    print(f"  - Use DC-t: {diffusion_model.net.use_dc_t}")
    print(f"  - Network type: {type(diffusion_model.net).__name__}")

    return diffusion_model, config


def test_forward_pass(diffusion_model, config):
    """Test forward pass through the model"""
    print("\nTesting forward pass...")

    batch_size = 4
    horizon = config.horizon
    action_dim = config.action_feature.shape[0]
    robot_state_dim = config.robot_state_feature.shape[0]

    # Create test batch (same format as training)
    batch = {
        'action': torch.randn(batch_size, horizon, action_dim),
        'observation.state': torch.randn(batch_size, config.n_obs_steps, robot_state_dim)
    }

    # Test standard diffusion loss
    loss_no_dc = diffusion_model.compute_loss(batch)

    # Test contrastive error computation
    error_matrix = diffusion_model.compute_contrastive_error(batch, use_dc=True)

    print(f"✓ Forward pass successful")
    print(f"  - Action shape: {batch['action'].shape}")
    print(f"  - Standard loss: {loss_no_dc.item():.4f}")
    print(f"  - Error matrix shape: {error_matrix.shape}")
    print(f"  - Diagonal error mean: {torch.diag(error_matrix).mean().item():.4f}")

    return loss_no_dc, error_matrix


def test_contrastive_training(diffusion_model):
    """Test contrastive learning setup"""
    print("\nTesting contrastive training...")

    batch_size = 8
    horizon = 16
    action_dim = 7
    robot_state_dim = 64

    # Create test batch
    batch = {
        'action': torch.randn(batch_size, horizon, action_dim),
        'observation.state': torch.randn(batch_size, 1, robot_state_dim)  # n_obs_steps=1
    }

    # Create trainer and loss
    trainer = SoftREPATrainer(diffusion_model)
    contrastive_loss = ContrastiveLoss(device='cpu')

    # Forward pass
    error_matrix = trainer(batch, use_dc=True)
    loss = contrastive_loss(error_matrix)

    print(f"✓ Contrastive training successful")
    print(f"  - Error matrix shape: {error_matrix.shape}")
    print(f"  - Loss value: {loss.item():.4f}")
    print(f"  - Diagonal mean: {torch.diag(error_matrix).mean().item():.4f}")
    print(f"  - Off-diagonal mean: {error_matrix[~torch.eye(batch_size, dtype=bool)].mean().item():.4f}")

    return error_matrix, loss


def test_parameter_freezing(diffusion_model):
    """Test that only DC parameters are trainable"""
    print("\nTesting parameter freezing...")

    # Freeze base model
    diffusion_model.freeze_base_model()

    # Check trainable parameters
    trainable_params = diffusion_model.get_dc_parameters()
    all_params = list(diffusion_model.named_parameters())

    dc_param_count = 0
    non_dc_param_count = 0

    for name, param in all_params:
        if 'dc' in name:
            dc_param_count += 1
            if not param.requires_grad:
                print(f"  ✗ DC parameter {name} is not trainable!")
        else:
            non_dc_param_count += 1
            if param.requires_grad:
                print(f"  ✗ Non-DC parameter {name} is trainable!")

    print(f"✓ Parameter freezing successful")
    print(f"  - Total parameters: {len(all_params)}")
    print(f"  - DC parameters: {dc_param_count}")
    print(f"  - Non-DC parameters: {non_dc_param_count}")
    print(f"  - Trainable parameters: {len(trainable_params)}")


def test_inference(diffusion_model, config):
    """Test inference functionality"""
    print("\nTesting inference...")

    # Create inference engine
    inference = TransformerDCInference(diffusion_model, num_inference_steps=10)  # Fewer steps for testing

    batch_size = 2
    robot_state_dim = config.robot_state_feature.shape[0]

    # Create test batch
    batch = {
        'observation.state': torch.randn(batch_size, config.n_obs_steps, robot_state_dim)
    }

    # Test sampling without DC
    actions_no_dc = inference.sample(
        batch=batch,
        use_dc=False
    )

    # Test sampling with DC
    actions_with_dc = inference.sample(
        batch=batch,
        use_dc=True
    )

    # Test guided sampling
    actions_guided = inference.sample_with_guidance(
        batch=batch,
        guidance_scale=1.5,
        use_dc=True
    )

    print(f"✓ Inference successful")
    print(f"  - Sample shape: {actions_no_dc.shape}")
    print(f"  - No DC mean/std: {actions_no_dc.mean():.4f} / {actions_no_dc.std():.4f}")
    print(f"  - With DC mean/std: {actions_with_dc.mean():.4f} / {actions_with_dc.std():.4f}")
    print(f"  - Guided mean/std: {actions_guided.mean():.4f} / {actions_guided.std():.4f}")

    return actions_no_dc, actions_with_dc, actions_guided


def test_gradient_flow(diffusion_model):
    """Test that gradients flow correctly through DC tokens"""
    print("\nTesting gradient flow...")

    # Freeze base model
    diffusion_model.freeze_base_model()

    batch_size = 4
    horizon = 16
    action_dim = 7
    robot_state_dim = 64

    # Create test batch
    batch = {
        'action': torch.randn(batch_size, horizon, action_dim),
        'observation.state': torch.randn(batch_size, 1, robot_state_dim)
    }

    # Forward pass
    trainer = SoftREPATrainer(diffusion_model)
    error_matrix = trainer(batch, use_dc=True)

    # Compute loss
    contrastive_loss = ContrastiveLoss(device='cpu')
    loss = contrastive_loss(error_matrix)

    # Backward pass
    loss.backward()

    # Check gradients
    dc_grads_exist = False
    non_dc_grads_exist = False

    for name, param in diffusion_model.named_parameters():
        if param.grad is not None:
            if 'dc' in name:
                dc_grads_exist = True
                print(f"  ✓ DC parameter {name} has gradient: {param.grad.norm().item():.6f}")
            else:
                non_dc_grads_exist = True
                print(f"  ✗ Non-DC parameter {name} has gradient!")

    print(f"✓ Gradient flow test {'passed' if dc_grads_exist and not non_dc_grads_exist else 'failed'}")
    print(f"  - DC gradients exist: {dc_grads_exist}")
    print(f"  - Non-DC gradients exist: {non_dc_grads_exist}")


def main():
    """Run all tests"""
    print("=" * 60)
    print("DiffusionModel_DC Test Suite")
    print("=" * 60)

    try:
        # Test 1: Basic creation
        diffusion_model, config = test_diffusion_model_creation()

        # Test 2: Forward pass
        test_forward_pass(diffusion_model, config)

        # Test 3: Contrastive training
        test_contrastive_training(diffusion_model)

        # Test 4: Parameter freezing
        test_parameter_freezing(diffusion_model)

        # Test 5: Inference
        test_inference(diffusion_model, config)

        # Test 6: Gradient flow
        test_gradient_flow(diffusion_model)

        print("\n" + "=" * 60)
        print("✓ All tests passed successfully!")
        print("The DiffusionModel_DC is working correctly.")
        print("=" * 60)

    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
