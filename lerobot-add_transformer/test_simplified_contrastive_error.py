#!/usr/bin/env python3
"""
测试简化后的compute_contrastive_error方法

验证只使用extend_batch方法的实现是否正确
"""

import torch
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss
)
from lerobot.configs.policies import PreTrainedConfig


def test_simplified_contrastive_error():
    """测试简化后的对比误差计算"""
    print("🧪 测试简化后的compute_contrastive_error方法")
    
    # 检查预训练模型路径
    checkpoint_paths = [
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000",
        "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000",
    ]
    
    checkpoint_path = None
    for path in checkpoint_paths:
        if Path(path).exists():
            checkpoint_path = path
            break
    
    if checkpoint_path is None:
        print("❌ 未找到预训练模型检查点")
        return False
    
    try:
        print(f"📁 使用检查点: {checkpoint_path}")
        
        # 加载配置和模型
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        
        model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=4,
            n_dc_layers=6,
            use_dc_t=True
        )
        
        model.eval()
        print("✅ 模型加载成功!")
        
        # 测试不同批次大小
        batch_sizes = [2, 4, 8]
        
        for batch_size in batch_sizes:
            print(f"\n📊 测试批次大小: {batch_size}")
            
            # 创建测试数据（匹配pusht数据格式）
            batch = {
                'action': torch.randn(batch_size, 16, 2),
                'observation.state': torch.randn(batch_size, 2, 2),  # 2维状态
                'observation.image': torch.randn(batch_size, 2, 3, 96, 96),  # 图像观察
            }
            
            # 测试计算时间
            start_time = time.time()
            
            with torch.no_grad():
                error_matrix = model.compute_contrastive_error(batch, use_dc=True)
            
            end_time = time.time()
            
            print(f"   ⏱️ 计算时间: {end_time - start_time:.4f}秒")
            print(f"   📐 误差矩阵形状: {error_matrix.shape}")
            print(f"   📈 对角线误差均值: {torch.diag(error_matrix).mean():.4f}")
            print(f"   📉 非对角线误差均值: {error_matrix[~torch.eye(batch_size, dtype=bool)].mean():.4f}")
            
            # 验证矩阵形状
            assert error_matrix.shape == (batch_size, batch_size), f"错误的矩阵形状: {error_matrix.shape}"
            
            # 测试对比损失
            contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device="cpu")
            loss = contrastive_loss(error_matrix)
            
            print(f"   🔥 对比损失: {loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_extend_batch_method():
    """测试扩展批次方法的正确性"""
    print("\n🧪 测试扩展批次方法")
    
    try:
        # 创建简单的测试数据
        batch_size = 3
        batch = {
            'action': torch.randn(batch_size, 16, 2),
            'observation.state': torch.randn(batch_size, 2, 2),
            'observation.image': torch.randn(batch_size, 2, 3, 96, 96),
        }
        
        # 创建一个简单的模型实例来测试方法
        from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
        
        config = DiffusionConfig(
            horizon=16,
            n_obs_steps=2,
            diffusion_step_embed_dim=128,
            n_layer=2,
            n_head=4,
            n_cond_layers=2,
            use_transformer=True,
        )
        
        model = DiffusionModel_DC(config, n_dc_tokens=2, n_dc_layers=2, use_dc_t=True)
        
        # 测试扩展批次方法
        extended_batch = model._create_extended_batch(batch)
        
        print(f"📊 原始批次大小: {batch_size}")
        print(f"📊 扩展后批次大小: {extended_batch['action'].shape[0]}")
        print(f"📊 期望扩展大小: {batch_size * batch_size}")
        
        # 验证扩展后的形状
        expected_size = batch_size * batch_size
        assert extended_batch['action'].shape[0] == expected_size, "动作扩展大小不正确"
        assert extended_batch['observation.state'].shape[0] == expected_size, "状态观察扩展大小不正确"
        assert extended_batch['observation.image'].shape[0] == expected_size, "图像观察扩展大小不正确"
        
        print("✅ 扩展批次方法测试通过!")
        
        # 测试损失计算
        losses = model._compute_batch_diffusion_losses(extended_batch)
        print(f"📊 损失形状: {losses.shape}")
        print(f"📊 期望损失形状: ({expected_size},)")
        
        assert losses.shape == (expected_size,), "损失形状不正确"
        print("✅ 批次损失计算测试通过!")
        
        return True
        
    except Exception as e:
        print(f"❌ 扩展批次测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("🚀 简化后的compute_contrastive_error测试\n")
    
    tests = [
        ("扩展批次方法", test_extend_batch_method),
        ("简化对比误差计算", test_simplified_contrastive_error),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print("=" * 60)
        success = test_func()
        results.append((test_name, success))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！简化后的compute_contrastive_error工作正常")
        print("\n✨ 简化优势:")
        print("   ✅ 代码更简洁，只使用extend_batch方法")
        print("   ✅ 逻辑更清晰，易于理解和维护")
        print("   ✅ 功能完整，保持对比学习效果")
        print("   ✅ 性能稳定，支持不同批次大小")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
