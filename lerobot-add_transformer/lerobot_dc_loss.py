import torch
import torch.nn as nn
import torch.nn.functional as F
import einops
from typing import Dict, Tensor

class ContrastiveLoss(nn.Module):
    def __init__(self, temp=0.07, scale=4.0, device='cuda', dweight=0):
        super().__init__()
        self.device = device
        self.temp = torch.nn.Parameter(torch.tensor(temp).to(self.device))
        self.scale = torch.nn.Parameter(torch.tensor(scale).to(self.device))
        self.dweight = dweight

    def get_mask(self, shape=None):
        mask = torch.zeros(shape, device=self.device)
        n_b, n_p = shape
        index = torch.arange(n_b, device=self.device)
        mask[index, index] = 1
        return mask

    def forward(self, errors):
        masks = self.get_mask(shape=errors.shape)
        logits = self.scale * torch.exp(-errors/self.temp)
        loss = F.cross_entropy(logits, masks)
        loss += self.dweight * errors[list(range(masks.shape[0])), list(range(masks.shape[0]))].mean()
        return loss


class DiffusionModelWithContrastive(nn.Module):
    """
    基于原始DiffusionModel，添加对比学习功能
    """
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 原有的组件构建 (简化表示，实际应该复制原代码)
        self._build_original_components()
        
        # 对比学习组件
        self.contrastive_loss = ContrastiveLoss(
            temp=getattr(config, 'contrastive_temp', 0.07),
            scale=getattr(config, 'contrastive_scale', 4.0),
            device=next(self.parameters()).device,
            dweight=getattr(config, 'contrastive_dweight', 0)
        )
        self.contrastive_weight = getattr(config, 'contrastive_weight', 1.0)
        self.use_contrastive = getattr(config, 'use_contrastive', False)

    def _build_original_components(self):
        """构建原始DiffusionModel的所有组件"""
        # 这里应该包含原始代码中的所有初始化
        # 观察编码器、网络、噪声调度器等
        pass

    def compute_error_matrix_with_compute_loss(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        使用循环调用 compute_loss 来计算 error matrix
        
        Args:
            batch: 原始批次数据
            
        Returns:
            error_matrix: (B, B) 矩阵，[i,j] 表示 action_i 与 condition_j 的重建误差
        """
        batch_size = batch["action"].shape[0]
        device = batch["action"].device
        error_matrix = torch.zeros(batch_size, batch_size, device=device)
        
        # 提取所有需要的observation字段
        observation_keys = [key for key in batch.keys() if key.startswith("observation")]
        
        for i in range(batch_size):      # action 索引
            for j in range(batch_size):  # condition (observation) 索引
                
                # 构造特殊的batch：action_i 配对 observation_j
                modified_batch = self._create_paired_batch(batch, i, j, observation_keys)
                
                # 使用原始的 compute_loss 计算这个配对的损失
                # 临时禁用填充遮蔽以获得纯净的重建误差
                original_mask_setting = self.config.do_mask_loss_for_padding
                self.config.do_mask_loss_for_padding = False
                
                try:
                    loss = self._compute_original_diffusion_loss(modified_batch)
                    error_matrix[i, j] = loss.item()
                finally:
                    # 恢复原始设置
                    self.config.do_mask_loss_for_padding = original_mask_setting
        
        return error_matrix

    def _create_paired_batch(self, original_batch: Dict[str, Tensor], 
                           action_idx: int, obs_idx: int, 
                           observation_keys: list) -> Dict[str, Tensor]:
        """
        创建一个特殊的batch，将第action_idx个action与第obs_idx个observation配对
        
        Args:
            original_batch: 原始batch
            action_idx: 要使用的action索引
            obs_idx: 要使用的observation索引
            observation_keys: 所有observation相关的键
            
        Returns:
            modified_batch: 配对后的batch
        """
        modified_batch = {}
        
        # 1. Action相关：取第action_idx个
        modified_batch["action"] = original_batch["action"][action_idx:action_idx+1]  # (1, horizon, action_dim)
        
        if "action_is_pad" in original_batch:
            modified_batch["action_is_pad"] = original_batch["action_is_pad"][action_idx:action_idx+1]
        
        # 2. Observation相关：取第obs_idx个
        for key in observation_keys:
            modified_batch[key] = original_batch[key][obs_idx:obs_idx+1]  # (1, ...)
        
        # 3. 其他字段：如果存在的话也要处理
        for key in original_batch:
            if key not in modified_batch:  # 如果还没处理过
                if key.startswith("observation") or key in ["action", "action_is_pad"]:
                    continue  # 已经处理过了
                else:
                    # 其他字段，根据语义决定取哪个索引
                    # 一般来说，环境相关的应该跟observation一起
                    modified_batch[key] = original_batch[key][obs_idx:obs_idx+1]
        
        return modified_batch

    def compute_error_matrix_efficient(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        高效版本：批量计算error matrix，减少循环开销
        
        这个版本同时计算多个配对，减少Python循环的开销
        """
        batch_size = batch["action"].shape[0]
        device = batch["action"].device
        
        # 创建所有可能的配对
        # actions: (B, horizon, action_dim) -> (B*B, horizon, action_dim)
        actions = batch["action"]  # (B, horizon, action_dim)
        expanded_actions = actions.unsqueeze(1).repeat(1, batch_size, 1, 1)  # (B, B, horizon, action_dim)
        expanded_actions = expanded_actions.reshape(batch_size * batch_size, *actions.shape[1:])  # (B*B, horizon, action_dim)
        
        # observations: 为每个action配对所有可能的observation
        observation_keys = [key for key in batch.keys() if key.startswith("observation")]
        expanded_observations = {}
        
        for key in observation_keys:
            obs = batch[key]  # (B, ...)
            # 为每个action重复所有observation
            expanded_obs = obs.unsqueeze(0).repeat(batch_size, 1, *([1] * (obs.dim() - 1)))  # (B, B, ...)
            expanded_observations[key] = expanded_obs.reshape(batch_size * batch_size, *obs.shape[1:])  # (B*B, ...)
        
        # 处理action_is_pad
        expanded_action_is_pad = None
        if "action_is_pad" in batch:
            action_is_pad = batch["action_is_pad"]  # (B, horizon)
            expanded_action_is_pad = action_is_pad.unsqueeze(1).repeat(1, batch_size, 1)  # (B, B, horizon)
            expanded_action_is_pad = expanded_action_is_pad.reshape(batch_size * batch_size, action_is_pad.shape[1])  # (B*B, horizon)
        
        # 构造大batch
        large_batch = {
            "action": expanded_actions,
            **expanded_observations
        }
        if expanded_action_is_pad is not None:
            large_batch["action_is_pad"] = expanded_action_is_pad
        
        # 计算损失（每个样本独立）
        # 临时禁用填充遮蔽
        original_mask_setting = self.config.do_mask_loss_for_padding
        self.config.do_mask_loss_for_padding = False
        
        try:
            # 计算每个配对的损失
            losses = self._compute_loss_per_sample(large_batch)  # (B*B,)
            error_matrix = losses.reshape(batch_size, batch_size)  # (B, B)
        finally:
            self.config.do_mask_loss_for_padding = original_mask_setting
        
        return error_matrix

    def _compute_loss_per_sample(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        计算每个样本的独立损失，返回 (batch_size,) 的张量
        """
        # 编码图像特征和其他观察
        global_cond = self._prepare_global_conditioning(batch)
        
        # 前向扩散
        trajectory = batch["action"]
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)
        
        # 网络预测
        pred = self.net(noisy_trajectory, timesteps, global_cond=global_cond)
        
        # 计算损失
        if self.config.prediction_type == "epsilon":
            target = eps
        elif self.config.prediction_type == "sample":
            target = batch["action"]
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")
        
        # 计算每个样本的损失（不求平均）
        loss = F.mse_loss(pred, target, reduction='none')  # (B, horizon, action_dim)
        loss_per_sample = loss.mean(dim=(1, 2))  # (B,) - 每个样本的平均损失
        
        return loss_per_sample

    def compute_loss_with_contrastive(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        计算总损失：原始扩散损失 + 对比损失
        """
        # 原始扩散损失
        original_loss = self._compute_original_diffusion_loss(batch)
        
        if not self.use_contrastive:
            return original_loss
        
        # 计算对比损失
        # 选择高效版本或循环版本
        if batch["action"].shape[0] <= 8:  # 小batch用循环版本（更清晰）
            error_matrix = self.compute_error_matrix_with_compute_loss(batch)
        else:  # 大batch用高效版本
            error_matrix = self.compute_error_matrix_efficient(batch)
        
        contrastive_loss = self.contrastive_loss(error_matrix)
        
        # 总损失
        total_loss = original_loss + self.contrastive_weight * contrastive_loss
        
        return total_loss

    def _compute_original_diffusion_loss(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        原始的扩散损失计算（从原代码复制，这里简化表示）
        """
        # 输入验证
        assert set(batch).issuperset({"observation.state", "action", "action_is_pad"})
        assert "observation.images" in batch or "observation.environment_state" in batch
        
        # 编码图像特征和其他观察
        global_cond = self._prepare_global_conditioning(batch)
        
        # 前向扩散
        trajectory = batch["action"]
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)
        
        # 网络预测
        pred = self.net(noisy_trajectory, timesteps, global_cond=global_cond)
        
        # 计算损失
        if self.config.prediction_type == "epsilon":
            target = eps
        elif self.config.prediction_type == "sample":
            target = batch["action"]
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")
        
        loss = F.mse_loss(pred, target, reduction="none")
        
        # 遮蔽填充
        if self.config.do_mask_loss_for_padding:
            if "action_is_pad" not in batch:
                raise ValueError("You need to provide 'action_is_pad' when masking is enabled.")
            in_episode_bound = ~batch["action_is_pad"]
            loss = loss * in_episode_bound.unsqueeze(-1)
        
        return loss.mean()

    def _prepare_global_conditioning(self, batch: Dict[str, Tensor]) -> Tensor:
        """
        准备全局条件（从原代码复制，这里简化表示）
        """
        # 这里应该是原代码的实现
        # 处理机器人状态、图像特征、环境状态等
        pass


# 使用示例
class DiffusionConfig:
    def __init__(self):
        # 基础配置
        self.robot_state_feature = type('obj', (object,), {'shape': [64]})()
        self.action_feature = type('obj', (object,), {'shape': [7]})()
        self.image_features = ['camera1', 'camera2']
        self.env_state_feature = None
        self.n_obs_steps = 3
        self.n_action_steps = 8
        self.horizon = 16
        self.num_train_timesteps = 1000
        self.prediction_type = "epsilon"
        self.do_mask_loss_for_padding = True
        
        # 对比学习配置
        self.use_contrastive = True
        self.contrastive_temp = 0.07
        self.contrastive_scale = 4.0
        self.contrastive_weight = 1.0
        self.contrastive_dweight = 0.1


if __name__ == "__main__":
    # 测试代码
    config = DiffusionConfig()
    model = DiffusionModelWithContrastive(config)
    
    # 模拟batch数据
    batch = {
        "action": torch.randn(4, 16, 7),  # (batch_size, horizon, action_dim)
        "action_is_pad": torch.zeros(4, 16, dtype=torch.bool),  # (batch_size, horizon)
        "observation.state": torch.randn(4, 3, 64),  # (batch_size, n_obs_steps, state_dim)
        "observation.images": torch.randn(4, 3, 2, 3, 224, 224),  # (batch_size, n_obs_steps, num_cameras, C, H, W)
    }
    
    # 计算error matrix (两种方法)
    print("方法1：循环调用 compute_loss")
    error_matrix_1 = model.compute_error_matrix_with_compute_loss(batch)
    print(f"Error matrix shape: {error_matrix_1.shape}")
    print(f"Diagonal (correct pairs): {torch.diag(error_matrix_1)}")
    
    print("\n方法2：高效批量计算")
    error_matrix_2 = model.compute_error_matrix_efficient(batch)
    print(f"Error matrix shape: {error_matrix_2.shape}")
    print(f"Diagonal (correct pairs): {torch.diag(error_matrix_2)}")
    
    # 验证两种方法结果是否一致
    print(f"\n两种方法结果差异: {torch.max(torch.abs(error_matrix_1 - error_matrix_2))}")