#!/usr/bin/env python3
"""
DiffusionModel_DC使用示例

展示如何在你的代码中创建、加载和使用DiffusionModel_DC模型（SoftREPA风格的DC tokens）
"""

import torch
import torch.nn as nn
from pathlib import Path

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    create_diffusion_model_dc,
    TransformerDCInference
)
from lerobot.configs.policies import PreTrainedConfig


class DiffusionDCWrapper:
    """
    DiffusionModel_DC的包装类
    简化模型的创建、加载和使用，支持SoftREPA风格的DC tokens
    """

    def __init__(self, checkpoint_path: str, n_dc_tokens: int = 4, n_dc_layers: int = 6,
                 use_dc_t: bool = True, device: str = "cpu"):
        self.device = torch.device(device)
        self.diffusion_model = None
        self.config = None
        self.n_dc_tokens = n_dc_tokens
        self.n_dc_layers = n_dc_layers
        self.use_dc_t = use_dc_t

        # 加载模型
        self.load_model(checkpoint_path)

    def load_model(self, checkpoint_path: str):
        """加载DiffusionModel_DC模型"""
        print("🤖 加载DiffusionModel_DC模型（带DC tokens）...")

        # 1. 加载配置
        config_path = Path(checkpoint_path) / "pretrained_model" / "config.json"
        if config_path.exists():
            self.config = PreTrainedConfig.from_pretrained(str(config_path.parent))
        else:
            # 如果没有config.json，尝试直接从checkpoint_path加载
            self.config = PreTrainedConfig.from_pretrained(checkpoint_path)

        print(f"配置类型: {self.config.type}")
        print(f"DC tokens配置: {self.n_dc_tokens} tokens, {self.n_dc_layers} layers, use_dc_t={self.use_dc_t}")

        # 2. 使用工厂函数创建DiffusionModel_DC实例
        try:
            self.diffusion_model = create_diffusion_model_dc(
                config=self.config,
                n_dc_tokens=self.n_dc_tokens,
                n_dc_layers=self.n_dc_layers,
                use_dc_t=self.use_dc_t,
                pretrained_model_path=checkpoint_path
            )
            print("✅ DiffusionModel_DC加载成功!")
        except Exception as e:
            print(f"❌ 从预训练模型加载失败: {e}")
            print("尝试创建新的DiffusionModel_DC...")
            self.diffusion_model = DiffusionModel_DC(
                config=self.config,
                n_dc_tokens=self.n_dc_tokens,
                n_dc_layers=self.n_dc_layers,
                use_dc_t=self.use_dc_t
            )
            print("✅ 新DiffusionModel_DC创建成功!")

        # 3. 移动到指定设备
        self.diffusion_model.to(self.device)
        self.diffusion_model.eval()

        print(f"📱 模型已加载到设备: {self.device}")
    
    def predict(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor):
        """
        使用DiffusionModel_DC进行预测

        Args:
            sample: [batch, horizon, action_dim] 输入样本
            timestep: [batch] 时间步
            global_cond: [batch, n_obs_steps * cond_dim] 全局条件

        Returns:
            [batch, horizon, action_dim] 预测输出
        """
        with torch.no_grad():
            # 确保输入在正确的设备上
            sample = sample.to(self.device)
            timestep = timestep.to(self.device)
            global_cond = global_cond.to(self.device)

            # 使用DiffusionModel_DC进行前向传播
            output = self.diffusion_model(sample, timestep, global_cond)

        return output

    def predict_with_dc_tokens(self, sample: torch.Tensor, timestep: torch.Tensor,
                              global_cond: torch.Tensor, return_dc_features: bool = False):
        """
        使用DC tokens进行预测（支持对比学习特征提取）

        Args:
            sample: [batch, horizon, action_dim] 输入样本
            timestep: [batch] 时间步
            global_cond: [batch, n_obs_steps * cond_dim] 全局条件
            return_dc_features: 是否返回DC token特征

        Returns:
            如果return_dc_features=False: [batch, horizon, action_dim] 预测输出
            如果return_dc_features=True: (预测输出, DC特征)
        """
        with torch.no_grad():
            # 确保输入在正确的设备上
            sample = sample.to(self.device)
            timestep = timestep.to(self.device)
            global_cond = global_cond.to(self.device)

            # 使用DiffusionModel_DC的内部网络进行前向传播
            if hasattr(self.diffusion_model.net, 'forward_with_dc_features'):
                if return_dc_features:
                    output, dc_features = self.diffusion_model.net.forward_with_dc_features(
                        sample, timestep, global_cond
                    )
                    return output, dc_features
                else:
                    output = self.diffusion_model.net(sample, timestep, global_cond)
                    return output
            else:
                # 回退到标准前向传播
                output = self.diffusion_model(sample, timestep, global_cond)
                return output
    
    def generate_action_sequence(self, observation: torch.Tensor, num_timesteps: int = 100):
        """
        使用DiffusionModel_DC生成动作序列（支持DC tokens的扩散采样）

        Args:
            observation: [batch, obs_dim] 观察
            num_timesteps: 扩散步数

        Returns:
            [batch, horizon, action_dim] 生成的动作序列
        """
        batch_size = observation.shape[0]
        horizon = self.config.horizon
        action_dim = self.config.action_feature.shape[0]
        n_obs_steps = self.config.n_obs_steps

        # 准备全局条件 (这里简化处理，实际应该根据具体情况构造)
        # 假设observation包含图像特征和状态
        obs_repeated = observation.unsqueeze(1).repeat(1, n_obs_steps, 1)  # 重复观察
        global_cond = obs_repeated.flatten(1)  # [batch, n_obs_steps * obs_dim]

        # 使用DiffusionModel_DC的采样方法
        if hasattr(self.diffusion_model, 'sample'):
            # 使用内置的采样方法
            sample = self.diffusion_model.sample(
                batch_size=batch_size,
                global_cond=global_cond,
                num_inference_steps=num_timesteps
            )
        else:
            # 回退到简化的扩散采样
            sample = torch.randn(batch_size, horizon, action_dim, device=self.device)

            for t in range(num_timesteps):
                timestep = torch.full((batch_size,), t, device=self.device)

                # 预测噪声（使用DC tokens）
                predicted_noise = self.predict(sample, timestep, global_cond)

                # 简单的去噪步骤（实际需要使用DDPM/DDIM调度器）
                alpha = 1.0 - t / num_timesteps
                sample = alpha * sample + (1 - alpha) * predicted_noise

        return sample

    def generate_with_inference_engine(self, observation: torch.Tensor, num_timesteps: int = 50):
        """
        使用TransformerDCInference引擎生成动作序列（推荐方法）

        Args:
            observation: [batch, obs_dim] 观察
            num_timesteps: 扩散步数

        Returns:
            [batch, horizon, action_dim] 生成的动作序列
        """
        # 创建推理引擎
        inference_engine = TransformerDCInference(self.diffusion_model)

        # 生成动作序列
        action_sequence = inference_engine.generate_actions(
            observation=observation,
            num_inference_steps=num_timesteps
        )

        return action_sequence
    
    @property
    def model_info(self):
        """获取DiffusionModel_DC模型信息"""
        base_info = {
            "model_type": "DiffusionModel_DC",
            "embedding_dim": self.config.diffusion_step_embed_dim,
            "num_encoder_layers": self.config.n_cond_layers,
            "num_decoder_layers": self.config.n_layer,
            "num_heads": self.config.n_head,
            "horizon": self.config.horizon,
            "n_obs_steps": self.config.n_obs_steps,
            "total_parameters": sum(p.numel() for p in self.diffusion_model.parameters()),
            "device": str(self.device)
        }

        # 添加DC tokens相关信息
        dc_info = {
            "n_dc_tokens": self.n_dc_tokens,
            "n_dc_layers": self.n_dc_layers,
            "use_dc_t": self.use_dc_t,
            "dc_parameters": sum(p.numel() for name, p in self.diffusion_model.named_parameters()
                               if 'dc' in name.lower())
        }

        return {**base_info, **dc_info}


def example_1_basic_usage():
    """示例1: DiffusionModel_DC基本使用"""
    print("=== 示例1: DiffusionModel_DC基本使用 ===")

    # 创建DiffusionModel_DC包装器
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"
    model = DiffusionDCWrapper(
        checkpoint_path=checkpoint_path,
        n_dc_tokens=4,      # DC tokens数量
        n_dc_layers=6,      # DC层数
        use_dc_t=True,      # 使用时间相关的DC tokens
        device="cpu"        # 或 "cuda" 如果有GPU
    )

    # 打印模型信息
    print("DiffusionModel_DC信息:")
    for key, value in model.model_info.items():
        print(f"  {key}: {value}")

    # 创建测试输入
    batch_size = 1
    sample = torch.randn(batch_size, 16, 2)  # [batch, horizon, action_dim]
    timestep = torch.randint(0, 100, (batch_size,))  # [batch]
    global_cond = torch.randn(batch_size, 132)  # [batch, n_obs_steps * cond_dim]

    # 标准预测
    output = model.predict(sample, timestep, global_cond)
    print(f"预测输出形状: {output.shape}")
    print(f"预测统计: min={output.min():.4f}, max={output.max():.4f}")

    # 使用DC tokens的预测（如果支持）
    try:
        dc_output, dc_features = model.predict_with_dc_tokens(
            sample, timestep, global_cond, return_dc_features=True
        )
        print(f"DC预测输出形状: {dc_output.shape}")
        if dc_features is not None:
            print(f"DC特征形状: {dc_features.shape}")
    except Exception as e:
        print(f"DC tokens预测不可用: {e}")


def example_2_action_generation():
    """示例2: 使用DiffusionModel_DC生成动作序列"""
    print("\n=== 示例2: DiffusionModel_DC动作生成 ===")

    # 创建DiffusionModel_DC
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"
    model = DiffusionDCWrapper(
        checkpoint_path=checkpoint_path,
        n_dc_tokens=4,
        n_dc_layers=6,
        use_dc_t=True,
        device="cpu"
    )

    # 模拟观察输入（例如：图像特征 + 状态）
    batch_size = 2
    obs_dim = 66  # 64维图像特征 + 2维状态
    observation = torch.randn(batch_size, obs_dim)

    # 方法1: 使用标准扩散采样
    print("方法1: 标准扩散采样")
    action_sequence = model.generate_action_sequence(observation, num_timesteps=50)
    print(f"生成的动作序列形状: {action_sequence.shape}")
    print(f"动作统计: min={action_sequence.min():.4f}, max={action_sequence.max():.4f}")

    # 方法2: 使用推理引擎（如果可用）
    print("\n方法2: 使用TransformerDCInference引擎")
    try:
        action_sequence_2 = model.generate_with_inference_engine(observation, num_timesteps=50)
        print(f"推理引擎生成的动作序列形状: {action_sequence_2.shape}")
        print(f"动作统计: min={action_sequence_2.min():.4f}, max={action_sequence_2.max():.4f}")
    except Exception as e:
        print(f"推理引擎不可用: {e}")


def example_3_batch_processing():
    """示例3: DiffusionModel_DC批量处理"""
    print("\n=== 示例3: DiffusionModel_DC批量处理 ===")

    # 创建DiffusionModel_DC
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"
    model = DiffusionDCWrapper(
        checkpoint_path=checkpoint_path,
        n_dc_tokens=4,
        n_dc_layers=6,
        use_dc_t=True,
        device="cpu"
    )

    # 批量输入
    batch_size = 8
    horizon = 16
    action_dim = 2

    # 创建批量输入
    samples = torch.randn(batch_size, horizon, action_dim)
    timesteps = torch.randint(0, 100, (batch_size,))
    global_conds = torch.randn(batch_size, 132)

    # 批量预测
    outputs = model.predict(samples, timesteps, global_conds)

    print(f"批量输入形状: {samples.shape}")
    print(f"批量输出形状: {outputs.shape}")
    print("✅ DiffusionModel_DC批量处理成功!")

    # 测试DC tokens批量处理
    print("\n测试DC tokens批量处理:")
    try:
        dc_outputs = model.predict_with_dc_tokens(samples, timesteps, global_conds)
        print(f"DC批量输出形状: {dc_outputs.shape}")
        print("✅ DC tokens批量处理成功!")
    except Exception as e:
        print(f"DC tokens批量处理失败: {e}")


def example_4_contrastive_learning():
    """示例4: 对比学习特征提取（SoftREPA风格）"""
    print("\n=== 示例4: 对比学习特征提取 ===")

    # 创建DiffusionModel_DC
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"
    model = DiffusionDCWrapper(
        checkpoint_path=checkpoint_path,
        n_dc_tokens=4,
        n_dc_layers=6,
        use_dc_t=True,
        device="cpu"
    )

    # 创建正负样本对（模拟对比学习场景）
    batch_size = 4
    obs_dim = 66

    # 正样本：相似的观察
    positive_obs = torch.randn(batch_size, obs_dim)
    # 负样本：不同的观察
    negative_obs = torch.randn(batch_size, obs_dim)

    print("提取对比学习特征...")
    try:
        # 提取正样本特征
        pos_actions = model.generate_action_sequence(positive_obs, num_timesteps=20)
        # 提取负样本特征
        neg_actions = model.generate_action_sequence(negative_obs, num_timesteps=20)

        print(f"正样本动作形状: {pos_actions.shape}")
        print(f"负样本动作形状: {neg_actions.shape}")

        # 计算特征相似度（简化版）
        pos_flat = pos_actions.flatten(1)
        neg_flat = neg_actions.flatten(1)
        similarity = torch.cosine_similarity(pos_flat, neg_flat, dim=1)
        print(f"正负样本相似度: {similarity.mean():.4f}")

        print("✅ 对比学习特征提取成功!")

    except Exception as e:
        print(f"对比学习特征提取失败: {e}")


def main():
    """主函数 - 运行所有DiffusionModel_DC示例"""
    print("🚀 DiffusionModel_DC使用示例（SoftREPA风格）\n")

    try:
        # 检查必要文件是否存在
        checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000"

        if not Path(checkpoint_path).exists():
            print(f"❌ 检查点路径不存在: {checkpoint_path}")
            print("请确保已训练好DiffusionModel并保存了检查点")
            return

        # 运行示例
        example_1_basic_usage()
        example_2_action_generation()
        example_3_batch_processing()
        example_4_contrastive_learning()

        print("\n🎉 所有DiffusionModel_DC示例运行完成!")
        print("\n💡 使用提示:")
        print("1. DiffusionModel_DC支持SoftREPA风格的DC tokens对比学习")
        print("2. 可以从预训练的DiffusionModel检查点加载权重")
        print("3. DC tokens参数会随机初始化，需要进一步训练优化")
        print("4. 建议使用TransformerDCInference进行高效推理")
        print("5. 根据需要调整n_dc_tokens、n_dc_layers和use_dc_t参数")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 