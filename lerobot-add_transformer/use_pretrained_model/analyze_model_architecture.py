#!/usr/bin/env python3
"""
详细的模型架构分析工具

分析LeRobot DiffusionPolicy模型的详细架构，特别是TransformerForDiffusion部分
"""

import torch
import json
from pathlib import Path
from typing import Dict, Any
import pandas as pd

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import DiffusionPolicy, TransformerForDiffusion
from lerobot.configs.policies import PreTrainedConfig


def analyze_model_architecture(checkpoint_path: str) -> Dict[str, Any]:
    """详细分析模型架构"""
    print(f"正在分析模型架构: {checkpoint_path}")
    
    # 加载模型
    config = PreTrainedConfig.from_pretrained(checkpoint_path)
    policy = DiffusionPolicy.from_pretrained(
        pretrained_name_or_path=str(checkpoint_path),
        config=config
    )
    
    # 将config转换为字典，处理不同的config类型
    try:
        config_dict = config.to_dict()
    except AttributeError:
        # 如果没有to_dict方法，使用vars()
        config_dict = vars(config)
    
    analysis = {
        "config": config_dict,
        "model_structure": {},
        "parameter_analysis": {},
        "transformer_details": {}
    }
    
    # 1. 总体结构分析
    print("\n=== 总体模型结构 ===")
    total_params = sum(p.numel() for p in policy.parameters())
    trainable_params = sum(p.numel() for p in policy.parameters() if p.requires_grad)
    
    analysis["parameter_analysis"] = {
        "total_parameters": total_params,
        "trainable_parameters": trainable_params,
        "non_trainable_parameters": total_params - trainable_params,
        "parameter_breakdown": {}
    }
    
    print(f"总参数量: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    print(f"不可训练参数: {total_params - trainable_params:,}")
    
    # 2. 模块级别参数分析
    print("\n=== 模块级别参数分析 ===")
    module_params = {}
    for name, module in policy.named_modules():
        if len(list(module.children())) == 0:  # 叶子模块
            param_count = sum(p.numel() for p in module.parameters())
            if param_count > 0:
                module_params[name] = {
                    "type": type(module).__name__,
                    "parameters": param_count,
                    "percentage": (param_count / total_params) * 100
                }
                print(f"{name:50} | {type(module).__name__:20} | {param_count:>10,} ({param_count/total_params*100:.2f}%)")
    
    analysis["parameter_analysis"]["parameter_breakdown"] = module_params
    
    # 3. TransformerForDiffusion详细分析
    transformer_module = None
    for name, module in policy.named_modules():
        if isinstance(module, TransformerForDiffusion):
            transformer_module = module
            transformer_name = name
            break
    
    if transformer_module:
        print(f"\n=== TransformerForDiffusion详细分析 ({transformer_name}) ===")
        
        transformer_params = sum(p.numel() for p in transformer_module.parameters())
        print(f"Transformer总参数量: {transformer_params:,} ({transformer_params/total_params*100:.2f}%)")
        
        # 分析Transformer内部结构
        transformer_details = {
            "total_parameters": transformer_params,
            "architecture": {
                "embedding_dim": getattr(config, 'diffusion_step_embed_dim', 'N/A'),
                "num_encoder_layers": getattr(config, 'n_cond_layers', 'N/A'),
                "num_decoder_layers": getattr(config, 'n_layer', 'N/A'),
                "num_heads": getattr(config, 'n_head', 'N/A'),
                "horizon": getattr(config, 'horizon', 'N/A'),
                "n_obs_steps": getattr(config, 'n_obs_steps', 'N/A')
            },
            "component_parameters": {}
        }
        
        # 分析各组件参数
        component_params = {}
        for name, param in transformer_module.named_parameters():
            component = name.split('.')[0]  # 获取顶级组件名
            if component not in component_params:
                component_params[component] = 0
            component_params[component] += param.numel()
        
        print("\nTransformer组件参数分布:")
        for component, param_count in sorted(component_params.items()):
            print(f"  {component:20} | {param_count:>10,} ({param_count/transformer_params*100:.2f}%)")
            transformer_details["component_parameters"][component] = {
                "parameters": param_count,
                "percentage": (param_count / transformer_params) * 100
            }
        
        analysis["transformer_details"] = transformer_details
        
        # 详细的参数层级分析
        print(f"\n=== 详细参数层级分析 ===")
        param_hierarchy = {}
        for name, param in transformer_module.named_parameters():
            parts = name.split('.')
            current = param_hierarchy
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            current[parts[-1]] = {
                "shape": list(param.shape),
                "parameters": param.numel(),
                "dtype": str(param.dtype)
            }
        
        analysis["transformer_details"]["parameter_hierarchy"] = param_hierarchy
    
    # 4. 配置参数分析
    print(f"\n=== 重要配置参数 ===")
    important_configs = [
        "use_transformer", "diffusion_step_embed_dim", "n_layer", "n_head", 
        "n_cond_layers", "horizon", "n_obs_steps", "causal_attn", 
        "p_drop_emb", "p_drop_attn"
    ]
    
    for key in important_configs:
        if hasattr(config, key):
            value = getattr(config, key)
            print(f"  {key:25} | {value}")
    
    return analysis


def save_analysis(analysis: Dict[str, Any], output_path: str):
    """保存分析结果"""
    output_file = Path(output_path) / "model_architecture_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False, default=str)
    print(f"\n分析结果已保存到: {output_file}")
    
    # 创建参数分布的CSV文件
    if "parameter_breakdown" in analysis["parameter_analysis"]:
        param_data = []
        for name, info in analysis["parameter_analysis"]["parameter_breakdown"].items():
            param_data.append({
                "模块名": name,
                "模块类型": info["type"],
                "参数量": info["parameters"],
                "占比(%)": info["percentage"]
            })
        
        df = pd.DataFrame(param_data)
        df = df.sort_values("参数量", ascending=False)
        csv_file = Path(output_path) / "parameter_breakdown.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"参数分布表已保存到: {csv_file}")


def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 3:
        print("使用方法: python analyze_model_architecture.py <checkpoint_path> <output_dir>")
        print("示例: python analyze_model_architecture.py ./checkpoints/190000/pretrained_model ./analysis_results")
        sys.exit(1)
    
    checkpoint_path = sys.argv[1]
    output_dir = sys.argv[2]
    
    try:
        # 创建输出目录
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # 执行分析
        analysis = analyze_model_architecture(checkpoint_path)
        
        # 保存结果
        save_analysis(analysis, output_dir)
        
        print("\n✅ 模型架构分析完成!")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 