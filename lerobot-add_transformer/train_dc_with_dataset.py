#!/usr/bin/env python

"""
Complete SoftREPA training script using lerobot dataset and safetensors checkpoint.
This script demonstrates the full workflow from checkpoint loading to DC token training.
"""

import argparse
import logging
import sys
import os
from pathlib import Path

import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from safetensors.torch import load_file

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset

from lerobot.common.policies.diffusion.transformer_dc_sampler import (
    DiffusionModel_DC,
    ContrastiveLoss,
)
from lerobot.common.utils.utils import init_logging, get_safe_torch_device


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dc_training.log')
        ]
    )


def create_dataset_config(dataset_repo_id: str):
    """
    简化的数据集配置创建函数

    Args:
        dataset_repo_id: Dataset repository ID (e.g., "lerobot/pusht")

    Returns:
        简单的配置对象
    """
    # 创建简单的配置对象
    class SimpleConfig:
        def __init__(self, repo_id):
            self.dataset = SimpleDatasetConfig(repo_id)
            self.policy = SimplePolicyConfig()
            self.training = True

    class SimpleDatasetConfig:
        def __init__(self, repo_id):
            self.repo_id = repo_id
            self.root = None
            self.revision = None

    class SimplePolicyConfig:
        def __init__(self):
            self.n_obs_steps = 2
            self.n_action_steps = 16
            self.horizon = 16

    return SimpleConfig(dataset_repo_id)


def load_and_create_dc_model(checkpoint_path: str, n_dc_tokens: int = 4, n_dc_layers: int = 6):
    """
    简化的DiffusionModel_DC创建和加载函数

    Args:
        checkpoint_path: Path to checkpoint directory
        n_dc_tokens: Number of DC tokens per layer
        n_dc_layers: Number of layers to apply DC tokens

    Returns:
        DiffusionModel_DC instance
    """
    checkpoint_dir = Path(checkpoint_path)

    logging.info(f"🔄 Loading DiffusionModel_DC from checkpoint: {checkpoint_path}")

    # 1. 加载预训练模型配置
    config_path = checkpoint_dir / "pretrained_model" / "config.json"
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    # 使用PreTrainedConfig加载配置（与简化后的代码一致）
    from lerobot.configs.policies import PreTrainedConfig
    config = PreTrainedConfig.from_pretrained(str(config_path.parent))

    logging.info(f"✅ 配置加载成功: {config.type}")

    # 2. 使用简化的from_pretrained方法创建DiffusionModel_DC
    try:
        diffusion_model = DiffusionModel_DC.from_pretrained(
            pretrained_model_path=checkpoint_path,
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=True
        )

        logging.info("✅ DiffusionModel_DC创建和加载成功!")

        # 3. 打印模型统计信息
        total_params = sum(p.numel() for p in diffusion_model.parameters())
        dc_params = sum(p.numel() for name, p in diffusion_model.named_parameters() if 'dc' in name.lower())
        base_params = total_params - dc_params

        logging.info(f"📊 模型参数统计:")
        logging.info(f"  总参数量: {total_params:,}")
        logging.info(f"  DC参数量: {dc_params:,} ({dc_params/total_params*100:.2f}%)")
        logging.info(f"  基础参数量: {base_params:,}")

        return diffusion_model

    except Exception as e:
        logging.error(f"❌ DiffusionModel_DC创建失败: {e}")
        raise


def train_dc_tokens(
    diffusion_model: DiffusionModel_DC,
    dataloader: DataLoader,
    device: torch.device,
    learning_rate: float = 1e-4,
    steps: int = 10000,
    log_freq: int = 100,
    save_freq: int = 1000,
    output_dir: str = "./dc_checkpoints"
):
    """
    Train DC tokens using contrastive learning.
    
    Args:
        diffusion_model: DiffusionModel_DC instance
        dataloader: Training data loader
        device: Training device
        learning_rate: Learning rate for DC token training
        steps: Number of training steps
        log_freq: Logging frequency
        save_freq: Checkpoint saving frequency
        output_dir: Output directory for checkpoints
    """
    # Move model to device
    diffusion_model.to(device)
    
    # Freeze base model and get DC parameters
    diffusion_model.freeze_base_model()
    dc_params = diffusion_model.get_dc_parameters()
    
    if not dc_params:
        raise ValueError("No DC parameters found! Check DC token setup.")
    
    logging.info(f"Training {len(dc_params)} DC parameters")
    diffusion_model.print_parameter_stats()
    
    # Create optimizer for DC parameters only with adjusted learning rate
    # 根据梯度分析，梯度范数约为1e-10，需要大幅增加学习率
    effective_lr = learning_rate * 1000  # 增加1000倍学习率
    logging.info(f"📈 调整学习率: {learning_rate:.0e} -> {effective_lr:.0e} (增加1000倍)")

    # 使用SGD优化器，更直接的梯度更新
    optimizer = torch.optim.SGD(dc_params, lr=effective_lr, momentum=0.9, weight_decay=1e-5)
    logging.info(f"🔧 使用SGD优化器，momentum=0.9, weight_decay=1e-5")

    # Create contrastive learning components with adjusted parameters
    # 大幅增加温度参数以获得更强的梯度信号
    adjusted_temp = 0.5  # 从0.07增加到0.5，提供更强的梯度信号
    adjusted_scale = 10.0  # 增加scale以放大梯度信号
    contrastive_loss = ContrastiveLoss(
        temp=adjusted_temp,
        scale=adjusted_scale,
        device=device.type,
        dweight=1.0  # 大幅增加扩散损失权重，提供直接的梯度信号
    )
    logging.info(f"🎯 对比损失参数: temp={adjusted_temp}, scale={adjusted_scale}, dweight=1.0")

    # 添加直接的DC token正则化损失
    def dc_regularization_loss(model, lambda_reg=0.01):
        """直接的DC token正则化，提供额外的梯度信号"""
        reg_loss = 0.0
        if hasattr(model.net, 'dc_tokens'):
            # L2正则化，鼓励DC tokens学习有意义的表示
            reg_loss += lambda_reg * torch.norm(model.net.dc_tokens, p=2)
        if hasattr(model.net, 'dc_t_tokens'):
            reg_loss += lambda_reg * torch.norm(model.net.dc_t_tokens.weight, p=2)
        return reg_loss

    # Create gradient scaler for mixed precision
    scaler = GradScaler()
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Training loop
    logging.info("Starting DC token training...")
    diffusion_model.train()
    
    # Create infinite data iterator
    from itertools import cycle
    data_iter = cycle(dataloader)
    
    progress_bar = tqdm(range(steps), desc="Training DC tokens")

    # 初始化监控变量
    initial_dc_tokens = {}
    initial_base_params = {}

    # 记录初始DC tokens状态
    for name, param in diffusion_model.named_parameters():
        # 检查是否是DC相关参数
        is_dc_param = (
            'dc_token' in name or
            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
        )

        if is_dc_param and param.requires_grad:
            initial_dc_tokens[name] = param.data.clone()
        elif param.requires_grad:
            logging.warning(f"⚠️ 发现未冻结的非DC参数: {name}")
        elif not is_dc_param:
            # 记录一些基础模型参数用于验证冻结状态
            if len(initial_base_params) < 5:  # 只记录前5个用于检查
                initial_base_params[name] = param.data.clone()

    logging.info(f"📊 初始DC tokens数量: {len(initial_dc_tokens)}")
    logging.info(f"📊 初始基础参数样本数量: {len(initial_base_params)}")

    # 输出DC tokens初始状态
    logging.info(diffusion_model.get_dc_tokens_summary())

    for step in progress_bar:
        # Get batch
        batch = next(data_iter)

        # Debug: Print batch structure on first iteration
        if step == 0:
            logging.info("Batch structure:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    logging.info(f"  {key}: {value.shape} {value.dtype}")
                else:
                    logging.info(f"  {key}: {type(value)}")

        # Move batch to device
        for key in batch:
            if isinstance(batch[key], torch.Tensor):
                batch[key] = batch[key].to(device, non_blocking=True)
        
        # Forward pass with mixed precision
        with autocast():
            # Compute contrastive error matrix using DiffusionModel_DC directly
            error_matrix = diffusion_model.compute_contrastive_error(batch, use_dc=True)

            # Compute contrastive loss
            contrastive_loss_value = contrastive_loss(error_matrix)

            # 添加DC token正则化损失，提供直接的梯度信号
            reg_loss = dc_regularization_loss(diffusion_model, lambda_reg=0.1)

            # 总损失
            loss = contrastive_loss_value + reg_loss
        
        # Backward pass
        optimizer.zero_grad()
        scaler.scale(loss).backward()


        # 不进行梯度裁剪，保留所有梯度信号
        scaler.unscale_(optimizer)
        # effective_grad_clip = 10.0  # 使用更大的梯度裁剪值，或者完全不裁剪
        # torch.nn.utils.clip_grad_norm_(dc_params, effective_grad_clip)
        logging.info("🚫 跳过梯度裁剪，保留完整梯度信号")
        
        # Optimizer step
        scaler.step(optimizer)
        scaler.update()

        # 监控DC tokens和模型参数更新
        if step % 10 == 0 or step < 5:  # 前5步和每10步检查一次
            with torch.no_grad():
                # 检查DC tokens是否更新
                dc_updates = {}
                for name, param in diffusion_model.named_parameters():
                    # 检查是否是DC相关参数
                    is_dc_param = (
                        'dc_token' in name or
                        (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                        (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                    )

                    if is_dc_param and param.requires_grad:
                        if name in initial_dc_tokens:
                            diff = torch.norm(param.data - initial_dc_tokens[name]).item()
                            dc_updates[name] = diff

                # 检查基础模型参数是否被意外更新
                base_updates = {}
                for name, param in diffusion_model.named_parameters():
                    if name in initial_base_params:
                        diff = torch.norm(param.data - initial_base_params[name]).item()
                        base_updates[name] = diff

                # 检查梯度
                dc_grad_norms = {}
                base_grad_norms = {}
                for name, param in diffusion_model.named_parameters():
                    if param.grad is not None:
                        grad_norm = torch.norm(param.grad).item()

                        # 检查是否是DC相关参数
                        is_dc_param = (
                            'dc_token' in name or
                            (hasattr(diffusion_model.net, 'dc_tokens') and param is diffusion_model.net.dc_tokens) or
                            (hasattr(diffusion_model.net, 'dc_t_tokens') and param is diffusion_model.net.dc_t_tokens.weight)
                        )

                        if is_dc_param:
                            dc_grad_norms[name] = grad_norm
                        else:
                            base_grad_norms[name] = grad_norm

        # Compute metrics
        with torch.no_grad():
            batch_size = error_matrix.shape[0]
            diagonal_errors = torch.diag(error_matrix)
            off_diagonal_errors = error_matrix[~torch.eye(batch_size, dtype=bool, device=error_matrix.device)]

            # Compute accuracy: how many diagonal elements are smaller than their row mean
            row_means = error_matrix.mean(dim=1)  # Mean error for each row
            correct_predictions = (diagonal_errors < row_means).float()
            accuracy = correct_predictions.mean()
        
        # Update progress bar
        progress_bar.set_postfix({
            'total_loss': f"{loss.item():.4f}",
            'cont_loss': f"{contrastive_loss_value.item():.4f}",
            'reg_loss': f"{reg_loss.item():.4f}",
            'acc': f"{accuracy.item():.3f}",
            'diag': f"{diagonal_errors.mean().item():.4f}",
            'off_diag': f"{off_diagonal_errors.mean().item():.4f}"
        })

        # 详细日志输出
        if (step + 1) % log_freq == 0 or step < 5:
            logging.info(
                f"Step {step+1}/{steps}: "
                f"TotalLoss={loss.item():.4f}, "
                f"ContrastiveLoss={contrastive_loss_value.item():.4f}, "
                f"RegLoss={reg_loss.item():.4f}, "
                f"Acc={accuracy.item():.3f}, "
                f"DiagErr={diagonal_errors.mean().item():.4f}, "
                f"OffDiagErr={off_diagonal_errors.mean().item():.4f}"
            )

            # 输出DC tokens更新情况
            if 'dc_updates' in locals():
                logging.info("🔍 DC Tokens更新情况:")
                for name, diff in dc_updates.items():
                    logging.info(f"  {name}: 变化量={diff:.6f}")

                if dc_grad_norms:
                    logging.info("📈 DC Tokens梯度范数:")
                    for name, grad_norm in dc_grad_norms.items():
                        logging.info(f"  {name}: 梯度范数={grad_norm:.10f}")  # 增加精度
                else:
                    logging.warning("⚠️ 没有检测到DC tokens的梯度!")

                # 检查基础模型是否被意外更新
                if base_updates:
                    max_base_update = max(base_updates.values())
                    if max_base_update > 1e-8:
                        logging.warning(f"⚠️ 基础模型参数可能未被正确冻结! 最大变化量: {max_base_update:.8f}")
                    else:
                        logging.info(f"✅ 基础模型参数正确冻结 (最大变化量: {max_base_update:.8f})")

                if base_grad_norms:
                    logging.warning("⚠️ 检测到基础模型参数的梯度:")
                    for name, grad_norm in list(base_grad_norms.items())[:3]:  # 只显示前3个
                        logging.warning(f"  {name}: 梯度范数={grad_norm:.6f}")
                else:
                    logging.info("✅ 基础模型参数没有梯度 (正确冻结)")

            # 检查损失是否在下降
            if step > 0:
                if hasattr(progress_bar, 'last_loss'):
                    loss_change = loss.item() - progress_bar.last_loss
                    if abs(loss_change) < 1e-6:
                        logging.warning(f"⚠️ 损失几乎没有变化: {loss_change:.8f}")
                    elif loss_change > 0:
                        logging.warning(f"⚠️ 损失上升: +{loss_change:.6f}")
                    else:
                        logging.info(f"✅ 损失下降: {loss_change:.6f}")

                progress_bar.last_loss = loss.item()
            else:
                progress_bar.last_loss = loss.item()
        
        # Save checkpoint
        if (step + 1) % save_freq == 0:
            checkpoint_path = Path(output_dir) / f"dc_checkpoint_step_{step+1}.pth"
            torch.save({
                'step': step + 1,
                'model_state_dict': diffusion_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss.item(),
                'accuracy': accuracy.item(),
            }, checkpoint_path)
            logging.info(f"Checkpoint saved: {checkpoint_path}")
    
    # Save final model
    final_path = Path(output_dir) / "dc_model_final.pth"
    torch.save({
        'step': steps,
        'model_state_dict': diffusion_model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss.item(),
        'accuracy': accuracy.item(),
    }, final_path)
    logging.info(f"Final model saved: {final_path}")


def main():
    parser = argparse.ArgumentParser(description="Train DiffusionModel_DC with lerobot dataset")
    
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="Path to lerobot checkpoint directory")
    parser.add_argument("--dataset", type=str, default="lerobot/pusht",
                       help="Dataset repository ID")
    parser.add_argument("--n_dc_tokens", type=int, default=4,
                       help="Number of DC tokens per layer")
    parser.add_argument("--n_dc_layers", type=int, default=6,
                       help="Number of layers to apply DC tokens")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use for training")
    parser.add_argument("--batch_size", type=int, default=64,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4,
                       help="Learning rate for DC token training")
    parser.add_argument("--steps", type=int, default=10000,
                       help="Number of training steps")
    parser.add_argument("--log_freq", type=int, default=100,
                       help="Logging frequency")
    parser.add_argument("--save_freq", type=int, default=1000,
                       help="Checkpoint saving frequency")
    parser.add_argument("--output_dir", type=str, default="./dc_checkpoints",
                       help="Output directory for checkpoints")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    init_logging()
    
    logging.info("=" * 60)
    logging.info("SoftREPA DC Token Training with Lerobot Dataset")
    logging.info("=" * 60)
    
    # Validate inputs
    if not Path(args.checkpoint).exists():
        logging.error(f"Checkpoint directory not found: {args.checkpoint}")
        return
    
    # Get device
    device = get_safe_torch_device(args.device, log=True)
    
    logging.info(f"Configuration:")
    logging.info(f"  Checkpoint: {args.checkpoint}")
    logging.info(f"  Dataset: {args.dataset}")
    logging.info(f"  DC tokens: {args.n_dc_tokens} tokens, {args.n_dc_layers} layers")
    logging.info(f"  Device: {device}")
    logging.info(f"  Batch size: {args.batch_size}")
    logging.info(f"  Learning rate: {args.learning_rate}")
    logging.info(f"  Training steps: {args.steps}")
    
    try:
        # Create dataset config
        dataset_config = create_dataset_config(args.dataset)
        
        # Load dataset
        logging.info("Loading dataset...")

        # Set up delta_timestamps for sequence loading
        delta_timestamps = {
            # Load 2 observation steps: previous and current
            "observation.state": [-0.1, 0.0],
            "observation.image": [-0.1, 0.0],
            # Load 16 action steps: current and 15 future actions
            "action": [i * 0.1 for i in range(16)],  # [0.0, 0.1, 0.2, ..., 1.5]
        }

        # Create LeRobotDataset directly
        from lerobot.common.datasets.lerobot_dataset import LeRobotDataset
        dataset = LeRobotDataset(
            repo_id=dataset_config.dataset.repo_id,
            delta_timestamps=delta_timestamps,
            image_transforms=None,  # No image transforms for now
        )
        logging.info(f"Dataset loaded: {len(dataset)} samples")
        
        # Create dataloader
        dataloader = DataLoader(
            dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=device.type != "cpu",
            drop_last=True
        )
        
        # Load model from checkpoint
        logging.info("Loading DiffusionModel_DC from checkpoint...")
        diffusion_model = load_and_create_dc_model(
            args.checkpoint,
            args.n_dc_tokens,
            args.n_dc_layers
        )
        
        # Train DC tokens
        train_dc_tokens(
            diffusion_model=diffusion_model,
            dataloader=dataloader,
            device=device,
            learning_rate=args.learning_rate,
            steps=args.steps,
            log_freq=args.log_freq,
            save_freq=args.save_freq,
            output_dir=args.output_dir
        )
        
        logging.info("Training completed successfully!")
        
    except Exception as e:
        logging.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
