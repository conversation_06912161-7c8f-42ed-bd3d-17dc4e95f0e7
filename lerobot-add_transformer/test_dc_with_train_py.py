#!/usr/bin/env python

"""
Test DC model using the standard train.py pipeline.
This script creates a proper configuration and uses make_policy to load the pretrained model.
"""

import logging
import sys
import os
from pathlib import Path
import time

# Add project root to path
sys.path.insert(0, '/home/<USER>/work/lerobot-add_transformer')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm
from omegaconf import OmegaConf

# Import lerobot components
from lerobot.common.datasets.factory import make_dataset
from lerobot.common.policies.factory import make_policy
from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionDCConfig
from lerobot.common.policies.diffusion.transformer_dc_sampler import DiffusionModel_DC
from lerobot.common.utils.utils import init_logging, get_safe_torch_device
from lerobot.configs.default import DatasetConfig


def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dc_train_py_test.log')
        ]
    )


def create_dc_policy_config(checkpoint_path: str, dataset_meta):
    """Create a DiffusionDCConfig for DC model that can be used with make_policy."""
    
    # Extract dimensions from dataset
    action_dim = dataset_meta.features["action"]["shape"][0]
    state_dim = dataset_meta.features["observation.state"]["shape"][0]
    has_images = "observation.image" in dataset_meta.features
    
    # Create the policy configuration
    config_dict = {
        # Basic diffusion config
        'horizon': 16,
        'n_obs_steps': 2,
        'use_transformer': True,
        'n_layer': 12,
        'n_cond_layers': 12,
        'n_head': 16,
        'diffusion_step_embed_dim': 1024,
        'vision_backbone': 'resnet18',
        'use_separate_rgb_encoder_per_camera': True,
        
        # Device and training settings
        'device': 'cuda',
        'use_amp': False,
        
        # DC-specific parameters
        'n_dc_tokens': 4,
        'n_dc_layers': 6,
        'use_dc_t': True,
        'dc_temp': 0.07,
        'dc_scale': 4.0,
        'dc_dweight': 0.0,

        # Features will be set by make_policy based on dataset
        'input_features': {},
        'output_features': {},
    }

    return DiffusionDCConfig(**config_dict)


def create_dataset_config(dataset_repo_id: str):
    """Create a dataset configuration."""
    from omegaconf import OmegaConf

    # Create a proper DatasetConfig
    dataset_config = DatasetConfig(
        repo_id=dataset_repo_id,
        root=None,  # Use default cache location
        revision=None,
        image_transforms=OmegaConf.create({
            "enable": False,  # Disable image transforms for simplicity
        })
    )

    # Wrap in the expected structure
    config = OmegaConf.create({
        "dataset": dataset_config,
        "policy": {
            "n_obs_steps": 2,  # Required for delta timestamps
            "n_action_steps": 16,  # Match the model's horizon
            "horizon": 16,  # Explicitly set horizon
            "observation_delta_indices": None,  # Required field
            "action_delta_indices": None,  # Required field
            "reward_delta_indices": None,  # Required field
        },
        "training": True,
    })

    return config


def test_dc_model_with_make_policy():
    """Test DC model using the standard make_policy approach."""
    
    setup_logging()
    
    # Configuration
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/195000"
    dataset_repo_id = "lerobot/pusht"
    device = get_safe_torch_device("cuda", log=True)
    
    print("=" * 60)
    print("Testing DC Model with make_policy")
    print("=" * 60)
    
    # Load dataset
    print("Loading dataset...")
    dataset_config = create_dataset_config(dataset_repo_id)
    dataset = make_dataset(dataset_config)
    print(f"Dataset loaded: {len(dataset)} samples")
    
    # Create policy configuration
    print("Creating policy configuration...")
    policy_config = create_dc_policy_config(checkpoint_path, dataset.meta)

    # Set pretrained path after config creation
    policy_config.pretrained_path = str(Path(checkpoint_path) / "pretrained_model")

    # Use make_policy to create the policy (this should load pretrained weights)
    print("Creating policy with make_policy...")
    try:
        policy = make_policy(
            cfg=policy_config,
            ds_meta=dataset.meta
        )
        print("✅ Policy created successfully!")
        print(f"Policy type: {type(policy)}")
        
        # Check if it's a DC model
        if hasattr(policy, 'compute_contrastive_error'):
            print("✅ DC model detected!")
        else:
            print("❌ Not a DC model - missing compute_contrastive_error method")
            
    except Exception as e:
        print(f"❌ Failed to create policy: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test forward pass
    print("\nTesting forward pass...")
    
    # Create dataloader
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=2,
        shuffle=True,
        num_workers=0,
        pin_memory=device.type != "cpu",
        drop_last=False,
    )
    
    # Test with a batch
    batch = next(iter(dataloader))
    
    # Move batch to device
    for key in batch:
        if isinstance(batch[key], torch.Tensor):
            batch[key] = batch[key].to(device, non_blocking=True)
    
    policy.train()
    
    try:
        # Test standard forward pass
        print("Testing standard forward pass...")
        output = policy(batch)
        print(f"✅ Standard forward output shape: {output.shape}")
        
        # Test DC forward pass if available
        if hasattr(policy, 'compute_contrastive_error'):
            print("Testing DC contrastive error computation...")
            error_matrix = policy.compute_contrastive_error(batch, use_dc=True)
            print(f"✅ DC error matrix shape: {error_matrix.shape}")
            print(f"DC error matrix values:\n{error_matrix}")
            
            # Test loss computation
            from lerobot.common.policies.diffusion.transformer_dc_sampler import ContrastiveLoss
            contrastive_loss = ContrastiveLoss(temp=0.07, scale=4.0, device=device.type)
            loss = contrastive_loss(error_matrix)
            print(f"✅ Contrastive loss: {loss.item()}")
            
            return True
        else:
            print("⚠️  DC methods not available")
            return False
            
    except Exception as e:
        print(f"❌ Error during forward pass: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    
    print("Testing DC Model Integration with train.py Pipeline")
    print("=" * 60)
    
    success = test_dc_model_with_make_policy()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! DC model works with make_policy!")
        print("✅ Ready to integrate with train.py")
    else:
        print("❌ Tests failed. Check the logs for details.")
    print("=" * 60)


if __name__ == "__main__":
    main()
