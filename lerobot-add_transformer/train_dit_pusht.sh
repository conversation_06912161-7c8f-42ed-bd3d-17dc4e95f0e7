# !/bin/bash

# DiT Policy Training Script for PushT
# ====================================
# # 如果你想从头开始训练，只需删除 outputs/train/diffusion_pusht_ditpolicy/checkpoints/last 目录即可。

# echo "🚀 Starting DiT Policy Training on PushT"
# echo "=========================================="

# Environment setup
export HF_ENDPOINT=https://hf-mirror.com
export PYTHONPATH=/home/<USER>/work/lerobot-add_transformer
export CUDA_VISIBLE_DEVICES=3
export http_proxy=http://localhost:7890
export https_proxy=http://localhost:7890

# Training command - Large DiT Policy (200M parameters)
/home/<USER>/.conda/envs/lerobot/bin/python lerobot/scripts/train.py \
  --policy.use_transformer=true \
  --policy.use_dit=false \
  --policy.use_separate_rgb_encoder_per_camera=true \
  --policy.diffusion_step_embed_dim=1024 \
  --policy.type=diffusion \
  --policy.n_layer=12 \
  --policy.n_cond_layers=12 \
  --policy.n_head=16 \
  --policy.device=cuda \
  --dataset.repo_id=lerobot/pusht \
  --dataset.video_backend=pyav \
  --env.type=pusht \
  --env.task=PushT-v0 \
  --batch_size=64 \
  --steps=200000 \
  --eval_freq=5000 \
  --save_freq=5000 \
  --output_dir=outputs/train/diffusion_pusht_transformer_edit_2 \
  --job_name=diffusion_pusht_transformer_edit \
  --seed=100000 \
  --save_checkpoint=true \
  --wandb.enable=true \
  --wandb.project=lerobot_dp \
  --wandb.disable_artifact=true

echo "🎉 Training completed!"

# Resume training command
# ========================

# # Check if a 'last' checkpoint exists to resume from
# CHECKPOINT_DIR="outputs/train/diffusion_pusht_ditpolicy/checkpoints/last"

# if [ -d "$CHECKPOINT_DIR" ]; then
#     echo "🔄 Resuming training from last checkpoint"
#     echo "=========================================="
#     CUDA_VISIBLE_DEVICES=7 \
#     HF_ENDPOINT=https://hf-mirror.com \
#     PYTHONPATH=/home/<USER>/work/lerobot-add_transformer \
#     /home/<USER>/.conda/envs/lerobot/bin/python lerobot/scripts/train.py \
#       --config_path="$CHECKPOINT_DIR/pretrained_model/" \
#       --resume=true \
#       --wandb.enable=true \
#       --wandb.project=lerobot_dp \
#       --wandb.disable_artifact=true \
#       --steps=200000
#     echo "✅ Resumed training finished!"
# else
#     echo "🏁 No checkpoint found, starting fresh training."
# fi
